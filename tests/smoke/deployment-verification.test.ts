import { describe, test, expect } from '@jest/globals';
import axios from 'axios';

const BASE_URL = process.env.TEST_BASE_URL || 'http://localhost:3000';

describe('Deployment Smoke Tests', () => {
  test('health endpoint should be accessible', async () => {
    const response = await axios.get(`${BASE_URL}/health`);
    expect(response.status).toBe(200);
    expect(response.data.status).toBe('ok');
    expect(response.data.database).toBe('connected');
  });

  test('API should be responsive', async () => {
    const response = await axios.get(`${BASE_URL}/api/health`);
    expect(response.status).toBe(200);
  });

  test('email processing endpoint should exist', async () => {
    // Test that endpoint exists (should return 400 for empty body, not 404)
    try {
      await axios.post(`${BASE_URL}/api/email/process`, '');
    } catch (error: any) {
      expect(error.response.status).toBe(400); // Bad request, not not found
      expect(error.response.data.message).toContain('No email data provided');
    }
  });

  test('OpenAPI documentation should be accessible', async () => {
    const response = await axios.get(`${BASE_URL}/docs`);
    expect(response.status).toBe(200);
  });
});

describe('Core Service Verification', () => {
  test('email parser should handle basic email', async () => {
    // This would require exposing a test endpoint or using the actual email processing
    // For now, we verify the endpoint exists and responds appropriately
    const testEmail = `From: <EMAIL>
To: <EMAIL>
Subject: Deployment Test
Message-ID: <<EMAIL>>

This is a deployment verification test.`;

    try {
      await axios.post(`${BASE_URL}/api/email/process`, testEmail, {
        headers: { 'Content-Type': 'text/plain' }
      });
    } catch (error: any) {
      // We expect this to fail (domain not configured), but it should fail 
      // with a proper error, not a server error
      expect([400, 404]).toContain(error.response.status);
      expect(error.response.data.message).toBeDefined();
    }
  });
});
