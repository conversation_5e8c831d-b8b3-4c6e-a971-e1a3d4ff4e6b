import { describe, test, expect } from '@jest/globals';
import { EmailParser } from '../../../src/backend/services/email-parser';

describe('Email Processing Core Logic', () => {
  test('should parse email to webhook payload correctly', async () => {
    const rawEmail = `From: <EMAIL>
To: <EMAIL>
Subject: Test Email
Message-ID: <<EMAIL>>
Date: ${new Date().toUTCString()}
Content-Type: text/plain

This is a test email body.`;

    const result = await EmailParser.parseToWebhookPayload(rawEmail, 'test.com');

    expect(result).toBeDefined();
    expect(result.message.sender.email).toBe('<EMAIL>');
    expect(result.message.recipient.email).toBe('<EMAIL>');
    expect(result.message.subject).toBe('Test Email');
    expect(result.message.content.text).toContain('This is a test email body');
    expect(result.envelope.messageId).toBe('<<EMAIL>>');
    expect(result.envelope.processed.domain).toBe('test.com');
  });

  test('should handle email with HTML content', async () => {
    const rawEmail = `From: <EMAIL>
To: <EMAIL>
Subject: HTML Email
Message-ID: <<EMAIL>>
Content-Type: text/html

<html><body><h1>HTML Content</h1><p>This is HTML email.</p></body></html>`;

    const result = await EmailParser.parseToWebhookPayload(rawEmail, 'test.com');

    expect(result.message.content.html).toContain('<h1>HTML Content</h1>');
    expect(result.message.content.html).toContain('<p>This is HTML email.</p>');
  });

  test('should handle email with attachments', async () => {
    const rawEmail = `From: <EMAIL>
To: <EMAIL>
Subject: Email with Attachment
Message-ID: <<EMAIL>>
Content-Type: multipart/mixed; boundary="boundary123"

--boundary123
Content-Type: text/plain

This email has an attachment.

--boundary123
Content-Type: text/plain; name="test.txt"
Content-Disposition: attachment; filename="test.txt"

This is attachment content.
--boundary123--`;

    const result = await EmailParser.parseToWebhookPayload(rawEmail, 'test.com');

    expect(result.message.attachments).toBeDefined();
    expect(Array.isArray(result.message.attachments)).toBe(true);
    // Note: Simple parser might not parse this complex multipart correctly
    // but we're testing the structure
  });

  test('should extract domain from email correctly', () => {
    const testCases = [
      { email: '<EMAIL>', expected: 'example.com' },
      { email: '<EMAIL>', expected: 'subdomain.example.org' },
      { email: '<EMAIL>', expected: 'uppercase.com' },
      { email: 'invalid-email', expected: '' },
      { email: '', expected: '' },
    ];

    testCases.forEach(({ email, expected }) => {
      const result = EmailParser.extractDomainFromEmail(email);
      expect(result).toBe(expected);
    });
  });

  test('should handle missing or malformed headers gracefully', async () => {
    const rawEmail = `Subject: No From Header
To: <EMAIL>

This email has no From header.`;

    const result = await EmailParser.parseToWebhookPayload(rawEmail, 'test.com');

    expect(result.message.sender.email).toBe('');
    expect(result.message.sender.name).toBeNull();
    expect(result.message.recipient.email).toBe('<EMAIL>');
    expect(result.message.subject).toBe('No From Header');
  });

  test('should generate message ID when missing', async () => {
    const rawEmail = `From: <EMAIL>
To: <EMAIL>
Subject: No Message ID

This email has no Message-ID header.`;

    const result = await EmailParser.parseToWebhookPayload(rawEmail, 'test.com');

    expect(result.envelope.messageId).toBeDefined();
    expect(result.envelope.messageId).toMatch(/^generated-\d+-/);
  });

  test('should include processing metadata', async () => {
    const rawEmail = `From: <EMAIL>
To: <EMAIL>
Subject: Metadata Test
Message-ID: <<EMAIL>>

Test email for metadata.`;

    const result = await EmailParser.parseToWebhookPayload(rawEmail, 'test.com');

    expect(result.envelope.processed).toBeDefined();
    expect(result.envelope.processed.domain).toBe('test.com');
    expect(result.envelope.processed.timestamp).toBeDefined();
    expect(result.envelope.processed.originalSize).toBeGreaterThan(0);
    expect(new Date(result.envelope.processed.timestamp)).toBeInstanceOf(Date);
  });

  test('should handle multiple recipients correctly', async () => {
    const rawEmail = `From: <EMAIL>
To: <EMAIL>, <EMAIL>
Cc: <EMAIL>
Subject: Multiple Recipients
Message-ID: <<EMAIL>>

Email with multiple recipients.`;

    const result = await EmailParser.parseToWebhookPayload(rawEmail, 'test.com');

    expect(result.message.recipient.email).toBe('<EMAIL>'); // Primary recipient
    expect(result.envelope.allRecipients.to).toContain('<EMAIL>');
    expect(result.envelope.allRecipients.to).toContain('<EMAIL>');
    expect(result.envelope.allRecipients.cc).toContain('<EMAIL>');
  });

  test('should preserve important headers', async () => {
    const rawEmail = `From: <EMAIL>
To: <EMAIL>
Subject: Header Test
Message-ID: <<EMAIL>>
X-Mailer: Test Mailer 1.0
Delivered-To: <EMAIL>
X-Original-To: <EMAIL>
Return-Path: <<EMAIL>>

Email with various headers.`;

    const result = await EmailParser.parseToWebhookPayload(rawEmail, 'test.com');

    expect(result.envelope.xMailer).toBe('Test Mailer 1.0');
    expect(result.envelope.deliveredTo).toContain('<EMAIL>');
    expect(result.envelope.xOriginalTo).toBe('<EMAIL>');
    expect(result.envelope.returnPath).toContain('<EMAIL>');
    expect(result.envelope.headers).toBeDefined();
    expect(typeof result.envelope.headers).toBe('object');
  });

  test('should handle email with display names', async () => {
    const rawEmail = `From: "John Doe" <<EMAIL>>
To: "Jane Smith" <<EMAIL>>
Subject: Display Names
Message-ID: <<EMAIL>>

Email with display names.`;

    const result = await EmailParser.parseToWebhookPayload(rawEmail, 'test.com');

    expect(result.message.sender.name).toBe('John Doe');
    expect(result.message.sender.email).toBe('<EMAIL>');
    expect(result.message.recipient.name).toBe('Jane Smith');
    expect(result.message.recipient.email).toBe('<EMAIL>');
  });

  test('should handle buffer input', async () => {
    const emailString = `From: <EMAIL>
To: <EMAIL>
Subject: Buffer Test
Message-ID: <<EMAIL>>

This email is provided as a buffer.`;

    const emailBuffer = Buffer.from(emailString, 'utf8');
    const result = await EmailParser.parseToWebhookPayload(emailBuffer, 'test.com');

    expect(result.message.sender.email).toBe('<EMAIL>');
    expect(result.message.recipient.email).toBe('<EMAIL>');
    expect(result.message.subject).toBe('Buffer Test');
    expect(result.envelope.messageId).toBe('<<EMAIL>>');
  });

  test('should handle date parsing correctly', async () => {
    const testDate = new Date('2023-12-25T10:30:00Z');
    const rawEmail = `From: <EMAIL>
To: <EMAIL>
Subject: Date Test
Message-ID: <<EMAIL>>
Date: ${testDate.toUTCString()}

Email with specific date.`;

    const result = await EmailParser.parseToWebhookPayload(rawEmail, 'test.com');

    expect(result.message.date).toBe(testDate.toISOString());
  });

  test('should use current date when email date is missing', async () => {
    const beforeParsing = new Date();
    
    const rawEmail = `From: <EMAIL>
To: <EMAIL>
Subject: No Date
Message-ID: <<EMAIL>>

Email without date header.`;

    const result = await EmailParser.parseToWebhookPayload(rawEmail, 'test.com');
    
    const afterParsing = new Date();
    const parsedDate = new Date(result.message.date);

    expect(parsedDate.getTime()).toBeGreaterThanOrEqual(beforeParsing.getTime());
    expect(parsedDate.getTime()).toBeLessThanOrEqual(afterParsing.getTime());
  });
});
