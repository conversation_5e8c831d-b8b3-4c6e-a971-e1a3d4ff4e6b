<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  size?: 'sm' | 'md' | 'lg'
  showText?: boolean
  textClass?: string
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  showText: true,
  textClass: 'text-base-content'
})

const sizeClasses = {
  sm: 'h-6',
  md: 'h-8',
  lg: 'h-12'
}

const textSizeClasses = {
  sm: 'text-lg',
  md: 'text-xl',
  lg: 'text-2xl'
}

// Determine which logo to use based on theme
const logoSrc = computed(() => {
  // Check if we're in dark mode by looking at the document's data-theme attribute
  if (typeof document !== 'undefined') {
    const theme = document.documentElement.getAttribute('data-theme')
    return theme === 'dim' ? '/logo-dark.png' : '/logo.png'
  }
  return '/logo.png'
})
</script>

<template>
  <div class="flex items-center space-x-3">
    <img
      :src="logoSrc"
      alt="Mail2Webhook.eu logo"
      :class="sizeClasses[props.size] + ' w-auto'"
    />
    <router-link to="/" v-if="props.showText" :class="[textSizeClasses[props.size], 'font-semibold', props.textClass]">
      Mail2Webhook.eu
    </router-link>
  </div>
</template>
